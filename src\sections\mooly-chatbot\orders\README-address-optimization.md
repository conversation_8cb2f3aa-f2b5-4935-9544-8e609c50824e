# Tối ưu hóa địa chỉ giao hàng - <PERSON><PERSON>bot

## Tổng quan

Hệ thống quản lý địa chỉ giao hàng đã được tối ưu hóa để phù hợp với địa chỉ Việt Nam và quốc tế, cung cấp trải nghiệm người dùng tốt hơn và quản lý dữ liệu chính xác hơn.

## Các tính năng mới

### 1. Dialog chỉnh sửa địa chỉ cải tiến

**File:** `src/sections/mooly-chatbot/orders/dialogs/order-edit-address-dialog.jsx`

#### Tính năng chính:
- **Tab interface**: Chuyển đổi giữa "Chỉnh sửa địa chỉ" và "Chọn địa chỉ có sẵn"
- **Chọn từ địa chỉ có sẵn**: Hiển thị danh sách địa chỉ đã lưu của khách hàng
- **Validation nâng cao**: <PERSON><PERSON><PERSON> tra số điện thoại V<PERSON>, đ<PERSON> dài trườ<PERSON>, ký tự hợp lệ
- **UI/UX cải thiện**: Helper text, placeholder rõ ràng, responsive design

#### Cách sử dụng:
```jsx
<OrderEditAddressDialog
  open={editAddressDialog.value}
  onClose={editAddressDialog.onFalse}
  order={order}
  shippingAddress={shippingAddress}
  billingAddress={billingAddress}
  onSuccess={() => {
    mutate();
    editAddressDialog.onFalse();
  }}
/>
```

### 2. Hiển thị địa chỉ tối ưu

**File:** `src/sections/mooly-chatbot/orders/order-summary.jsx`

#### Cải tiến:
- **Icon và layout mới**: Sử dụng icon để phân biệt các loại thông tin
- **Format số điện thoại**: Tự động format số điện thoại Việt Nam
- **Hiển thị thông minh**: Ẩn/hiện thông tin dựa trên quốc gia
- **Trạng thái validation**: Hiển thị trạng thái đầy đủ của địa chỉ

### 3. Utility functions

**File:** `src/sections/mooly-chatbot/orders/utils/address-formatter.js`

#### Các function chính:

```javascript
// Format địa chỉ đầy đủ
formatFullAddress(address)

// Format địa chỉ ngắn gọn
formatShortAddress(address)

// Format thông tin người nhận
formatRecipientInfo(address)

// Format số điện thoại Việt Nam
formatPhoneNumber(phone)

// Validate tính đầy đủ của địa chỉ
validateAddressCompleteness(address)

// Chuẩn hóa dữ liệu từ database
normalizeAddressData(rawAddress)

// Chuyển đổi dữ liệu cho database
transformAddressForDatabase(formData)
```

### 4. Validation Schema cải tiến

**File:** `src/sections/mooly-chatbot/orders/validation/order-edit-schemas.js`

#### Cải tiến:
- **Regex số điện thoại Việt Nam**: Kiểm tra chính xác format số điện thoại VN
- **Validation tên**: Chỉ cho phép chữ cái và khoảng trắng
- **Giới hạn độ dài**: Đặt giới hạn hợp lý cho các trường
- **Mã bưu chính**: Hỗ trợ format quốc tế

### 5. Components hỗ trợ

#### AddressValidationStatus
**File:** `src/sections/mooly-chatbot/orders/components/address-validation-status.jsx`

Hiển thị trạng thái validation của địa chỉ:
```jsx
<AddressValidationStatus 
  address={shippingAddress} 
  showDetails={true} 
/>
```

#### AddressQuickActions
**File:** `src/sections/mooly-chatbot/orders/components/address-quick-actions.jsx`

Cung cấp các hành động nhanh cho địa chỉ:
```jsx
<AddressQuickActions
  address={shippingAddress}
  onEdit={onEditAddress}
  onCopy={(address) => toast.success('Đã sao chép!')}
  onViewOnMap={() => toast.info('Đang mở bản đồ...')}
  size="small"
/>
```

## Cấu trúc Database

### Bảng `customer_addresses`

```sql
- id (UUID)
- customer_id (UUID)
- full_name (VARCHAR)
- phone (VARCHAR)
- address (TEXT) -- Địa chỉ cụ thể
- address_line2 (TEXT) -- Địa chỉ bổ sung
- ward (VARCHAR) -- Phường/Xã
- district (VARCHAR) -- Quận/Huyện  
- province (VARCHAR) -- Tỉnh/Thành phố
- city (VARCHAR) -- Thành phố (quốc tế)
- state (VARCHAR) -- Tiểu bang
- postal_code (VARCHAR) -- Mã bưu chính
- country (VARCHAR) -- Quốc gia
- notes (TEXT) -- Ghi chú
- is_default (BOOLEAN)
- is_default_shipping (BOOLEAN)
- is_default_billing (BOOLEAN)
- address_type (VARCHAR)
```

### Bảng `orders`

```sql
- shipping_address_id (UUID) -- FK to customer_addresses
- billing_address_id (UUID) -- FK to customer_addresses
```

## Quy trình sử dụng

### 1. Chỉnh sửa địa chỉ từ Order Detail

1. Mở order detail
2. Click nút "Chỉnh sửa" trong phần địa chỉ giao hàng
3. Chọn tab "Chọn địa chỉ có sẵn" nếu muốn dùng địa chỉ đã lưu
4. Hoặc chọn tab "Chỉnh sửa địa chỉ" để nhập/sửa thông tin
5. Điền đầy đủ thông tin theo hướng dẫn
6. Click "Cập nhật địa chỉ"

### 2. Validation tự động

- Hệ thống tự động kiểm tra tính đầy đủ của địa chỉ
- Hiển thị cảnh báo nếu thiếu thông tin quan trọng
- Validate format số điện thoại Việt Nam
- Kiểm tra độ dài và ký tự hợp lệ

### 3. Quick Actions

- **Sao chép địa chỉ**: Copy toàn bộ địa chỉ vào clipboard
- **Xem trên bản đồ**: Mở Google Maps với địa chỉ
- **Chỉnh sửa**: Mở dialog chỉnh sửa

## Lưu ý kỹ thuật

### 1. Performance
- Sử dụng lazy loading cho danh sách địa chỉ khách hàng
- Cache validation results
- Debounce cho các input field

### 2. Accessibility
- Đầy đủ ARIA labels
- Keyboard navigation
- Screen reader friendly

### 3. Responsive Design
- Mobile-first approach
- Flexible layout cho các màn hình khác nhau
- Touch-friendly buttons

### 4. Error Handling
- Graceful degradation khi API lỗi
- User-friendly error messages
- Retry mechanisms

## Tương lai

### Planned Features
1. **Address autocomplete**: Tích hợp Google Places API
2. **Bulk address operations**: Cập nhật nhiều địa chỉ cùng lúc
3. **Address templates**: Mẫu địa chỉ cho các khu vực phổ biến
4. **Delivery time estimation**: Ước tính thời gian giao hàng dựa trên địa chỉ
5. **Address verification**: Xác minh địa chỉ thực tế

### Technical Improvements
1. **Caching strategy**: Redis cache cho địa chỉ thường dùng
2. **Offline support**: PWA features cho mobile
3. **Real-time validation**: WebSocket cho validation real-time
4. **Analytics**: Tracking user behavior với địa chỉ
