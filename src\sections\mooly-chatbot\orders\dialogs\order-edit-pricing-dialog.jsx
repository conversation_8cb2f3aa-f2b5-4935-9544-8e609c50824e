'use client';

import { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

import { useOrderMutations } from 'src/actions/mooly-chatbot/order-mutations';
import { PAYMENT_METHOD_OPTIONS, SHIPPING_METHOD_OPTIONS } from 'src/actions/mooly-chatbot/order-constants';

import { toast } from 'src/components/snackbar';
import { Form, Field } from 'src/components/hook-form';

import { OrderPricingEditSchema } from '../validation/order-edit-schemas';
import { MenuItem } from '@mui/material';

// Utility function để format tiền tệ
const formatCurrency = (amount) => {
  if (isNaN(amount) || amount === null || amount === undefined) {
    return '0 ₫';
  }

  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(Number(amount));
};

// Utility function để parse số từ input
const parseNumber = (value) => {
  if (value === null || value === undefined || value === '') {
    return 0;
  }
  const parsed = Number(value);
  return isNaN(parsed) ? 0 : parsed;
};

// Utility function để tính tổng đơn hàng
const calculateOrderTotal = (subtotal, shippingAmount, taxAmount, discountAmount) => {
  const sub = parseNumber(subtotal);
  const ship = parseNumber(shippingAmount);
  const tax = parseNumber(taxAmount);
  const discount = parseNumber(discountAmount);

  return Math.max(0, sub + ship + tax - discount);
};

/*
TEST CASES cho calculateOrderTotal:
- calculateOrderTotal(1000000, 20000, 0, 0) = 1020000 (1M + 20K)
- calculateOrderTotal("1000000", "20000", "0", "0") = 1020000 (string inputs)
- calculateOrderTotal(1000000, 20000, 0, 50000) = 970000 (1M + 20K - 50K)
- calculateOrderTotal(100000, 20000, 0, 150000) = 0 (không âm)
- calculateOrderTotal(null, undefined, "", 0) = 0 (invalid inputs)
*/

// ----------------------------------------------------------------------

export function OrderEditPricingDialog({ open, onClose, order, onSuccess }) {
  const { updateOrder, isMutating } = useOrderMutations();

  const defaultValues = {
    subtotal: order?.subtotal || 0,
    shippingAmount: order?.shippingAmount || 0,
    discountAmount: order?.discountAmount || 0,
    taxAmount: order?.taxAmount || 0,
    paymentMethod: order?.paymentMethod || '',
    shippingMethod: order?.shippingMethod || '',
  };

  const methods = useForm({
    resolver: zodResolver(OrderPricingEditSchema),
    defaultValues,
  });

  const { handleSubmit, reset, watch } = methods;

  // Reset form khi dialog mở và có dữ liệu mới
  useEffect(() => {
    if (open && order) {
      const newValues = {
        subtotal: parseNumber(order?.subtotal),
        shippingAmount: parseNumber(order?.shippingAmount),
        discountAmount: parseNumber(order?.discountAmount),
        taxAmount: parseNumber(order?.taxAmount),
        paymentMethod: order?.paymentMethod || '',
        shippingMethod: order?.shippingMethod || '',
      };

      // Log chỉ trong development mode
      if (process.env.NODE_ENV === 'development') {
        console.log('Resetting form with values:', newValues);
        console.log('Original order data:', {
          subtotal: order?.subtotal,
          shippingAmount: order?.shippingAmount,
          discountAmount: order?.discountAmount,
          taxAmount: order?.taxAmount,
          types: {
            subtotal: typeof order?.subtotal,
            shippingAmount: typeof order?.shippingAmount,
            discountAmount: typeof order?.discountAmount,
            taxAmount: typeof order?.taxAmount,
          }
        });
      }

      reset(newValues);
    }
  }, [open, order, reset]);

  // Watch các giá trị để tính tổng - đảm bảo convert sang number
  const subtotal = parseNumber(watch('subtotal'));
  const shippingAmount = parseNumber(watch('shippingAmount'));
  const discountAmount = parseNumber(watch('discountAmount'));
  const taxAmount = parseNumber(watch('taxAmount'));

  // Tính tổng với logic chính xác
  const totalAmount = calculateOrderTotal(subtotal, shippingAmount, taxAmount, discountAmount);

  // Debug log để kiểm tra các giá trị - chỉ trong development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Pricing calculation debug:', {
        subtotal,
        shippingAmount,
        discountAmount,
        taxAmount,
        totalAmount,
        calculation: `${subtotal} + ${shippingAmount} + ${taxAmount} - ${discountAmount} = ${totalAmount}`,
        types: {
          subtotal: typeof subtotal,
          shippingAmount: typeof shippingAmount,
          discountAmount: typeof discountAmount,
          taxAmount: typeof taxAmount,
        }
      });
    }
  }, [subtotal, shippingAmount, discountAmount, taxAmount, totalAmount]);

  const onSubmit = handleSubmit(async (data) => {
    try {
      // Đảm bảo tất cả các giá trị là number
      const cleanData = {
        subtotal: parseNumber(data.subtotal),
        shippingAmount: parseNumber(data.shippingAmount),
        discountAmount: parseNumber(data.discountAmount),
        taxAmount: parseNumber(data.taxAmount),
        paymentMethod: data.paymentMethod,
        shippingMethod: data.shippingMethod,
      };

      // Tính lại totalAmount để đảm bảo chính xác
      const calculatedTotal = calculateOrderTotal(
        cleanData.subtotal,
        cleanData.shippingAmount,
        cleanData.taxAmount,
        cleanData.discountAmount
      );

      const updateData = {
        ...cleanData,
        totalAmount: calculatedTotal,
      };

      if (process.env.NODE_ENV === 'development') {
        console.log('Submitting pricing data:', updateData);
      }

      const result = await updateOrder(order.id, updateData);

      if (!result.success) {
        throw new Error(result.error || 'Cập nhật thông tin thanh toán thất bại');
      }

      toast.success('Cập nhật thông tin thanh toán thành công!');
      onSuccess?.();
      onClose();
    } catch (error) {
      console.error('Error updating pricing:', error);
      toast.error(error.message || 'Cập nhật thông tin thanh toán thất bại!');
    }
  });

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>Chỉnh sửa thông tin thanh toán</DialogTitle>

      <Form methods={methods} onSubmit={onSubmit}>
        <DialogContent>
          <Stack spacing={3} sx={{ pt: 1 }}>
            <Field.CurrencyInput
              name="subtotal"
              label="Tổng tiền hàng"
              type="number"
              required
              placeholder="0"
              inputProps={{
                min: 0,
                max: 999999999999, // 999 tỷ
                step: 1000,
              }}
              InputProps={{
                endAdornment: '₫',
              }}
              helperText="Nhập số tiền không bao gồm phí vận chuyển và thuế"
            />

            <Field.CurrencyInput
              name="shippingAmount"
              label="Phí vận chuyển"
              type="number"
              required
              placeholder="0"
              inputProps={{
                min: 0,
                max: 999999999999,
                step: 1000,
              }}
              InputProps={{
                endAdornment: '₫',
              }}
            />

            <Field.CurrencyInput
              name="discountAmount"
              label="Giảm giá"
              type="number"
              placeholder="0"
              inputProps={{
                min: 0,
                max: 999999999999,
                step: 1000,
              }}
              InputProps={{
                endAdornment: '₫',
              }}
              helperText="Số tiền giảm giá (nếu có)"
            />

            <Field.CurrencyInput
              name="taxAmount"
              label="Thuế"
              type="number"
              required
              placeholder="0"
              inputProps={{
                min: 0,
                max: 999999999999,
                step: 1000,
              }}
              InputProps={{
                endAdornment: '₫',
              }}
              helperText="Thuế VAT hoặc các loại thuế khác"
            />

            <Field.Select
              name="paymentMethod"
              label="Phương thức thanh toán"
              required
            >
              {PAYMENT_METHOD_OPTIONS.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Field.Select>

            <Field.Select
              name="shippingMethod"
              label="Phương thức vận chuyển"
              required
            >
              {SHIPPING_METHOD_OPTIONS.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Field.Select>

            {/* Hiển thị tổng cộng */}
            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems="center"
              sx={{
                p: 2,
                bgcolor: 'background.neutral',
                borderRadius: 1,
                border: '1px solid',
                borderColor: 'divider',
              }}
            >
              <span style={{ fontWeight: 'bold' }}>Tổng cộng:</span>
              <span style={{ fontWeight: 'bold', fontSize: '1.1rem', color: 'primary.main' }}>
                {formatCurrency(totalAmount)}
              </span>
            </Stack>
          </Stack>
        </DialogContent>

        <DialogActions>
          <Button onClick={handleClose} color="inherit">
            Hủy
          </Button>
          <LoadingButton
            type="submit"
            variant="contained"
            loading={isMutating}
          >
            Cập nhật
          </LoadingButton>
        </DialogActions>
      </Form>
    </Dialog>
  );
}

OrderEditPricingDialog.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  order: PropTypes.object,
  onSuccess: PropTypes.func,
};
