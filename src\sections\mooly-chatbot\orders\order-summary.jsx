'use client';

import PropTypes from 'prop-types';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';

import { fCurrency } from 'src/utils/format-number';
import { fDate, fTime } from 'src/utils/format-time';

import {
  ORDER_STATUS_OPTIONS,
  PAYMENT_METHOD_OPTIONS,
  SHIPPING_METHOD_OPTIONS,
} from 'src/actions/mooly-chatbot/order-constants';

import { Label } from 'src/components/label';
import { Iconify } from 'src/components/iconify';

import { toast } from 'src/components/snackbar';

import { getEditPermissions } from './utils/order-edit-permissions';
import { formatRecipientInfo, formatPhoneNumber } from './utils/address-formatter';
import { AddressValidationStatus } from './components/address-validation-status';
import { AddressQuickActions } from './components/address-quick-actions';

// ----------------------------------------------------------------------

export function OrderSummary({ order, shippingAddress, billingAddress, onEditBasic, onEditAddress, onEditPricing }) {
  if (!order) {
    return null;
  }

  const editPermissions = getEditPermissions(order.status);

  // Format recipient info using utility
  const recipientInfo = formatRecipientInfo(shippingAddress);

  const getStatusInfo = (status) => {
    const statusOption = ORDER_STATUS_OPTIONS.find((option) => option.value === status);
    return statusOption || { label: 'Không xác định', color: 'default' };
  };

  const getPaymentMethod = (method) => {
    const payment = PAYMENT_METHOD_OPTIONS.find((option) => option.value === method);
    return payment?.label || method;
  };

  const getShippingMethod = (method) => {
    const shipping = SHIPPING_METHOD_OPTIONS.find((option) => option.value === method);
    return shipping?.label || method;
  };

  const statusInfo = getStatusInfo(order.status);

  return (
    <Card sx={{ p: 3 }}>
      <Stack spacing={3}>
        <Stack
          spacing={2}
          direction={{ xs: 'column', sm: 'row' }}
          justifyContent={{ sm: 'space-between' }}
          alignItems={{ sm: 'center' }}
          sx={{ p: 3, bgcolor: 'background.neutral', borderRadius: 1 }}
        >
          <Stack direction="row" spacing={1} alignItems="center">
            <Typography variant="subtitle2">Mã đơn hàng:</Typography>
            <Typography variant="body2">{order.orderNumber}</Typography>
          </Stack>

          <Stack direction="row" spacing={1} alignItems="center">
            <Typography variant="subtitle2">Ngày tạo:</Typography>
            <Typography variant="body2">
              {fDate(order.createdAt)} {fTime(order.createdAt)}
            </Typography>
          </Stack>

          <Label variant="soft" color={statusInfo.color} sx={{ textTransform: 'capitalize' }}>
            {statusInfo.label}
          </Label>
        </Stack>

        <Stack spacing={2}>
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">Thông tin khách hàng</Typography>
            {editPermissions.basicInfo && onEditBasic && (
              <IconButton
                size="small"
                onClick={onEditBasic}
                sx={{ color: 'primary.main' }}
              >
                <Iconify icon="solar:pen-bold" width={16} />
              </IconButton>
            )}
          </Stack>

          <Stack spacing={2} sx={{ p: 3, bgcolor: 'background.neutral', borderRadius: 1 }}>
            <Stack direction="row" spacing={1}>
              <Typography variant="subtitle2" sx={{ width: 160 }}>
                Tên khách hàng:
              </Typography>
              <Typography variant="body2">{order.customerName}</Typography>
            </Stack>

            <Stack direction="row" spacing={1}>
              <Typography variant="subtitle2" sx={{ width: 160 }}>
                Số điện thoại:
              </Typography>
              <Typography variant="body2">{order.customerPhone}</Typography>
            </Stack>

            {order.customerEmail && (
              <Stack direction="row" spacing={1}>
                <Typography variant="subtitle2" sx={{ width: 160 }}>
                  Email:
                </Typography>
                <Typography variant="body2">{order.customerEmail}</Typography>
              </Stack>
            )}
          </Stack>
        </Stack>

        {shippingAddress && (
          <Stack spacing={2}>
            <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Stack direction="row" alignItems="center" spacing={2}>
                <Typography variant="h6">Địa chỉ giao hàng</Typography>
                <AddressValidationStatus address={shippingAddress} />
              </Stack>

              <AddressQuickActions
                address={shippingAddress}
                onEdit={editPermissions.address && onEditAddress ? onEditAddress : null}
                onCopy={(address) => toast.success('Đã sao chép địa chỉ!')}
                onViewOnMap={() => toast.info('Đang mở bản đồ...')}
                size="small"
              />
            </Stack>

            <Stack spacing={2} sx={{ p: 3, bgcolor: 'background.neutral', borderRadius: 1 }}>
              {/* Thông tin người nhận */}
              <Stack direction="row" spacing={1} alignItems="flex-start">
                <Iconify icon="solar:user-bold" sx={{ mt: 0.5, color: 'text.secondary' }} />
                <Stack spacing={0.5} sx={{ flex: 1 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Người nhận
                  </Typography>
                  <Typography variant="body2" fontWeight="medium">
                    {recipientInfo?.name || 'Chưa có thông tin'}
                  </Typography>
                </Stack>
              </Stack>

              {/* Số điện thoại */}
              <Stack direction="row" spacing={1} alignItems="flex-start">
                <Iconify icon="solar:phone-bold" sx={{ mt: 0.5, color: 'text.secondary' }} />
                <Stack spacing={0.5} sx={{ flex: 1 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Số điện thoại
                  </Typography>
                  <Typography variant="body2" fontWeight="medium">
                    {recipientInfo?.formattedPhone || recipientInfo?.phone || 'Chưa có thông tin'}
                  </Typography>
                </Stack>
              </Stack>

              {/* Địa chỉ chi tiết */}
              <Stack direction="row" spacing={1} alignItems="flex-start">
                <Iconify icon="solar:map-point-bold" sx={{ mt: 0.5, color: 'text.secondary' }} />
                <Stack spacing={0.5} sx={{ flex: 1 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Địa chỉ giao hàng
                  </Typography>
                  <Typography variant="body2">
                    {shippingAddress.address}
                    {shippingAddress.addressLine2 && (
                      <>
                        <br />
                        <Typography component="span" variant="body2" color="text.secondary">
                          {shippingAddress.addressLine2}
                        </Typography>
                      </>
                    )}
                  </Typography>

                  {/* Địa chỉ hành chính */}
                  {(shippingAddress.ward || shippingAddress.district || shippingAddress.province) && (
                    <Typography variant="body2" color="text.secondary">
                      {[
                        shippingAddress.ward,
                        shippingAddress.district,
                        shippingAddress.province || shippingAddress.city,
                      ]
                        .filter(Boolean)
                        .join(', ')}
                    </Typography>
                  )}

                  {/* Thông tin quốc tế */}
                  {(shippingAddress.city || shippingAddress.state || shippingAddress.postalCode) &&
                   shippingAddress.country !== 'Vietnam' && (
                    <Typography variant="body2" color="text.secondary">
                      {[shippingAddress.city, shippingAddress.state, shippingAddress.postalCode]
                        .filter(Boolean)
                        .join(', ')}
                    </Typography>
                  )}

                  {/* Quốc gia */}
                  {shippingAddress.country && shippingAddress.country !== 'Vietnam' && (
                    <Typography variant="body2" color="text.secondary">
                      {shippingAddress.country}
                    </Typography>
                  )}
                </Stack>
              </Stack>

              {/* Ghi chú giao hàng */}
              {shippingAddress.notes && (
                <Stack direction="row" spacing={1} alignItems="flex-start">
                  <Iconify icon="solar:notes-bold" sx={{ mt: 0.5, color: 'text.secondary' }} />
                  <Stack spacing={0.5} sx={{ flex: 1 }}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Ghi chú giao hàng
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        fontStyle: 'italic',
                        p: 1.5,
                        bgcolor: 'warning.lighter',
                        borderRadius: 1,
                        border: '1px solid',
                        borderColor: 'warning.light'
                      }}
                    >
                      {shippingAddress.notes}
                    </Typography>
                  </Stack>
                </Stack>
              )}
            </Stack>
          </Stack>
        )}

        <Stack spacing={2}>
          <Typography variant="h6">Thông tin thanh toán</Typography>

          <Stack spacing={2} sx={{ p: 3, bgcolor: 'background.neutral', borderRadius: 1 }}>
            <Stack direction="row" spacing={1}>
              <Typography variant="subtitle2" sx={{ width: 160 }}>
                Phương thức thanh toán:
              </Typography>
              <Typography variant="body2">{getPaymentMethod(order.paymentMethod)}</Typography>
            </Stack>

            <Stack direction="row" spacing={1}>
              <Typography variant="subtitle2" sx={{ width: 160 }}>
                Phương thức vận chuyển:
              </Typography>
              <Typography variant="body2">{getShippingMethod(order.shippingMethod)}</Typography>
            </Stack>
          </Stack>
        </Stack>

        <Stack spacing={2}>
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">Tổng thanh toán</Typography>
            {editPermissions.pricing && onEditPricing && (
              <IconButton
                size="small"
                onClick={onEditPricing}
                sx={{ color: 'primary.main' }}
              >
                <Iconify icon="solar:pen-bold" width={16} />
              </IconButton>
            )}
          </Stack>

          <Stack spacing={2} sx={{ p: 3, bgcolor: 'background.neutral', borderRadius: 1 }}>
            <Stack direction="row" justifyContent="space-between">
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Tổng tiền hàng
              </Typography>
              <Typography variant="subtitle2">{fCurrency(order.subtotal)}</Typography>
            </Stack>

            <Stack direction="row" justifyContent="space-between">
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Phí vận chuyển
              </Typography>
              <Typography variant="subtitle2">{fCurrency(order.shippingAmount)}</Typography>
            </Stack>

            <Stack direction="row" justifyContent="space-between">
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Giảm giá
              </Typography>
              <Typography variant="subtitle2">
                {order.discountAmount ? `-${fCurrency(order.discountAmount)}` : fCurrency(0)}
              </Typography>
            </Stack>

            <Stack direction="row" justifyContent="space-between">
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Thuế
              </Typography>
              <Typography variant="subtitle2">{fCurrency(order.taxAmount)}</Typography>
            </Stack>

            <Divider sx={{ borderStyle: 'dashed' }} />

            <Stack direction="row" justifyContent="space-between">
              <Typography variant="subtitle1">Tổng cộng</Typography>
              <Box sx={{ textAlign: 'right' }}>
                <Typography variant="subtitle1">{fCurrency(order.totalAmount)}</Typography>
              </Box>
            </Stack>
          </Stack>
        </Stack>

        {order.notes && (
          <Stack spacing={2}>
            <Typography variant="h6">Ghi chú</Typography>

            <Stack spacing={2} sx={{ p: 3, bgcolor: 'background.neutral', borderRadius: 1 }}>
              <Typography variant="body2">{order.notes}</Typography>
            </Stack>
          </Stack>
        )}
      </Stack>
    </Card>
  );
}

OrderSummary.propTypes = {
  order: PropTypes.object,
  shippingAddress: PropTypes.object,
  billingAddress: PropTypes.object,
  onEditBasic: PropTypes.func,
  onEditAddress: PropTypes.func,
  onEditPricing: PropTypes.func,
};
