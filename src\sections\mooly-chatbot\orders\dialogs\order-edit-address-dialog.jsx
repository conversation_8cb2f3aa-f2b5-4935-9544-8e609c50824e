'use client';

import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import Divider from '@mui/material/Divider';
import MenuItem from '@mui/material/MenuItem';
import Typography from '@mui/material/Typography';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

import { useOrderMutations } from 'src/actions/mooly-chatbot/order-mutations';
import { createData, updateData } from 'src/actions/mooly-chatbot/supabase-utils';
import { getCustomerAddresses } from 'src/actions/mooly-chatbot/customer-service';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { Form, Field } from 'src/components/hook-form';

import { OrderAddressEditSchema } from '../validation/order-edit-schemas';
import {
  formatFullAddress,
  normalizeAddressData,
  transformAddressForDatabase,
  validateAddressCompleteness
} from '../utils/address-formatter';

// ----------------------------------------------------------------------

export function OrderEditAddressDialog({ open, onClose, order, shippingAddress, billingAddress, onSuccess }) {
  const { updateOrder, isMutating } = useOrderMutations();

  // State management
  const [currentTab, setCurrentTab] = useState('edit');
  const [customerAddresses, setCustomerAddresses] = useState([]);
  const [isLoadingAddresses, setIsLoadingAddresses] = useState(false);

  const defaultValues = {
    fullName: shippingAddress?.fullName || order?.customerName || '',
    phone: shippingAddress?.phone || order?.customerPhone || '',
    address: shippingAddress?.address || '',
    addressLine2: shippingAddress?.addressLine2 || '',
    ward: shippingAddress?.ward || '',
    district: shippingAddress?.district || '',
    province: shippingAddress?.province || '',
    city: shippingAddress?.city || '',
    state: shippingAddress?.state || '',
    postalCode: shippingAddress?.postalCode || '',
    country: shippingAddress?.country || 'Vietnam',
    notes: shippingAddress?.notes || '',
  };

  const methods = useForm({
    resolver: zodResolver(OrderAddressEditSchema),
    defaultValues,
  });

  const { handleSubmit, reset } = methods;

  // Load customer addresses when dialog opens
  useEffect(() => {
    const loadAddresses = async () => {
      if (open && order?.customerId) {
        setIsLoadingAddresses(true);
        try {
          const result = await getCustomerAddresses(order.customerId);
          if (result.success && result.data) {
            setCustomerAddresses(result.data);
          }
        } catch (error) {
          console.error('Error loading customer addresses:', error);
        } finally {
          setIsLoadingAddresses(false);
        }
      }
    };

    loadAddresses();
  }, [open, order?.customerId]);

  // Reset form khi dialog mở và có dữ liệu mới
  useEffect(() => {
    if (open && (order || shippingAddress)) {
      const newValues = {
        fullName: shippingAddress?.fullName || order?.customerName || '',
        phone: shippingAddress?.phone || order?.customerPhone || '',
        address: shippingAddress?.address || '',
        addressLine2: shippingAddress?.addressLine2 || '',
        ward: shippingAddress?.ward || '',
        district: shippingAddress?.district || '',
        province: shippingAddress?.province || '',
        city: shippingAddress?.city || '',
        state: shippingAddress?.state || '',
        postalCode: shippingAddress?.postalCode || '',
        country: shippingAddress?.country || 'Vietnam',
        notes: shippingAddress?.notes || '',
      };
      reset(newValues);
    }
  }, [open, order, shippingAddress, reset]);

  const onSubmit = handleSubmit(async (data) => {
    try {
      // Validate địa chỉ trước khi submit
      const validation = validateAddressCompleteness(data);
      if (!validation.isComplete) {
        toast.error(`Thiếu thông tin: ${validation.missingFields.join(', ')}`);
        return;
      }

      // Hiển thị warnings nếu có
      if (validation.warnings.length > 0) {
        console.warn('Address warnings:', validation.warnings);
      }

      let addressId = order?.shippingAddressId || shippingAddress?.id;

      // Chuẩn bị dữ liệu địa chỉ sử dụng transformer
      const addressData = transformAddressForDatabase({
        customerId: order?.customerId,
        ...data,
        addressType: 'shipping',
        isDefault: false,
        isDefaultShipping: true,
        isDefaultBilling: false,
      });

      if (addressId) {
        // Cập nhật địa chỉ hiện có
        const addressResult = await updateData('customer_addresses', addressData, { id: addressId });
        if (!addressResult.success) {
          throw new Error(addressResult.error || 'Cập nhật địa chỉ thất bại');
        }
      } else {
        // Tạo địa chỉ mới
        const addressResult = await createData('customer_addresses', addressData);
        if (!addressResult.success) {
          throw new Error(addressResult.error || 'Tạo địa chỉ thất bại');
        }
        addressId = addressResult.data[0].id;

        // Cập nhật order với shipping_address_id mới
        const orderResult = await updateOrder(order.id, {
          shippingAddressId: addressId,
        });

        if (!orderResult.success) {
          throw new Error(orderResult.error || 'Cập nhật đơn hàng thất bại');
        }
      }

      toast.success('Cập nhật địa chỉ giao hàng thành công!');
      onSuccess?.();
      onClose();
    } catch (error) {
      console.error('Error updating address:', error);
      toast.error(error.message || 'Cập nhật địa chỉ thất bại!');
    }
  });

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  // Handle select existing address
  const handleSelectAddress = (address) => {
    const normalizedAddress = normalizeAddressData(address);
    const newValues = {
      fullName: normalizedAddress.fullName,
      phone: normalizedAddress.phone,
      address: normalizedAddress.address,
      addressLine2: normalizedAddress.addressLine2,
      ward: normalizedAddress.ward,
      district: normalizedAddress.district,
      province: normalizedAddress.province,
      city: normalizedAddress.city,
      state: normalizedAddress.state,
      postalCode: normalizedAddress.postalCode,
      country: normalizedAddress.country,
      notes: normalizedAddress.notes,
    };
    reset(newValues);
    setCurrentTab('edit');
    toast.success('Đã chọn địa chỉ thành công!');
  };

  const handleClose = () => {
    reset();
    setCurrentTab('edit');
    setCustomerAddresses([]);
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Stack direction="row" alignItems="center" spacing={1}>
          <Iconify icon="solar:map-point-bold" />
          <Typography variant="h6">Chỉnh sửa địa chỉ giao hàng</Typography>
        </Stack>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={currentTab} onChange={handleTabChange} sx={{ px: 3 }}>
            <Tab
              value="edit"
              label="Chỉnh sửa địa chỉ"
              icon={<Iconify icon="solar:pen-bold" />}
              iconPosition="start"
            />
            {order?.customerId && (
              <Tab
                value="select"
                label="Chọn địa chỉ có sẵn"
                icon={<Iconify icon="solar:bookmark-bold" />}
                iconPosition="start"
              />
            )}
          </Tabs>
        </Box>

        {/* Tab Content */}
        {currentTab === 'select' && order?.customerId && (
          <Box sx={{ p: 3 }}>
            <Typography variant="subtitle2" sx={{ mb: 2 }}>
              Chọn từ địa chỉ đã lưu của khách hàng:
            </Typography>

            {isLoadingAddresses ? (
              <Box sx={{ textAlign: 'center', py: 3 }}>
                <Typography variant="body2" color="text.secondary">
                  Đang tải địa chỉ...
                </Typography>
              </Box>
            ) : customerAddresses.length > 0 ? (
              <Stack spacing={2}>
                {customerAddresses.map((address) => (
                  <Card
                    key={address.id}
                    sx={{
                      p: 2,
                      cursor: 'pointer',
                      border: '1px solid',
                      borderColor: 'divider',
                      '&:hover': {
                        borderColor: 'primary.main',
                        bgcolor: 'action.hover',
                      }
                    }}
                    onClick={() => handleSelectAddress(address)}
                  >
                    <Stack spacing={1}>
                      <Stack direction="row" alignItems="center" spacing={1}>
                        <Typography variant="subtitle2">
                          {address.fullName || address.full_name}
                        </Typography>
                        {(address.isDefaultShipping || address.is_default_shipping) && (
                          <Typography
                            variant="caption"
                            sx={{
                              px: 1,
                              py: 0.5,
                              bgcolor: 'primary.main',
                              color: 'primary.contrastText',
                              borderRadius: 1,
                              fontSize: '0.75rem'
                            }}
                          >
                            Mặc định
                          </Typography>
                        )}
                      </Stack>
                      <Typography variant="body2" color="text.secondary">
                        {address.phone}
                      </Typography>
                      <Typography variant="body2">
                        {formatFullAddress(address)}
                      </Typography>
                    </Stack>
                  </Card>
                ))}
              </Stack>
            ) : (
              <Box sx={{ textAlign: 'center', py: 3 }}>
                <Typography variant="body2" color="text.secondary">
                  Khách hàng chưa có địa chỉ nào được lưu
                </Typography>
              </Box>
            )}
          </Box>
        )}

        {currentTab === 'edit' && (
          <Form methods={methods} onSubmit={onSubmit}>
            <Box sx={{ p: 3 }}>
              <Stack spacing={3}>
                <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
                  <Field.Text
                    name="fullName"
                    label="Tên người nhận"
                    required
                    placeholder="Nhập tên người nhận"
                  />

                  <Field.Text
                    name="phone"
                    label="Số điện thoại"
                    required
                    placeholder="Nhập số điện thoại"
                  />
                </Stack>

                <Field.Text
                  name="address"
                  label="Địa chỉ cụ thể"
                  required
                  placeholder="Số nhà, tên đường..."
                  helperText="VD: 123 Nguyễn Văn Linh"
                />

                <Field.Text
                  name="addressLine2"
                  label="Địa chỉ bổ sung (tùy chọn)"
                  placeholder="Tòa nhà, căn hộ, tầng..."
                  helperText="VD: Tầng 5, Tòa nhà ABC"
                />

                {/* Địa chỉ hành chính Việt Nam */}
                <Typography variant="subtitle2" sx={{ mt: 2, mb: 1 }}>
                  Địa chỉ hành chính
                </Typography>

                <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
                  <Field.Text
                    name="province"
                    label="Tỉnh/Thành phố"
                    placeholder="VD: Hồ Chí Minh"
                    helperText="Tỉnh/Thành phố"
                  />

                  <Field.Text
                    name="district"
                    label="Quận/Huyện"
                    placeholder="VD: Quận 1"
                    helperText="Quận/Huyện/Thị xã"
                  />

                  <Field.Text
                    name="ward"
                    label="Phường/Xã"
                    placeholder="VD: Phường Bến Nghé"
                    helperText="Phường/Xã/Thị trấn"
                  />
                </Stack>

                {/* Thông tin bổ sung (cho địa chỉ quốc tế) */}
                <Typography variant="subtitle2" sx={{ mt: 2, mb: 1 }}>
                  Thông tin bổ sung (tùy chọn)
                </Typography>

                <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
                  <Field.Text
                    name="city"
                    label="Thành phố (quốc tế)"
                    placeholder="VD: New York"
                    helperText="Dành cho địa chỉ quốc tế"
                  />

                  <Field.Text
                    name="state"
                    label="Tiểu bang"
                    placeholder="VD: California"
                    helperText="Dành cho địa chỉ quốc tế"
                  />

                  <Field.Text
                    name="postalCode"
                    label="Mã bưu chính"
                    placeholder="VD: 10001"
                    helperText="Zip code/Postal code"
                  />
                </Stack>

                <Field.Select
                  name="country"
                  label="Quốc gia"
                  helperText="Chọn quốc gia giao hàng"
                >
                  <MenuItem value="Vietnam">Việt Nam</MenuItem>
                  <MenuItem value="United States">Hoa Kỳ</MenuItem>
                  <MenuItem value="Singapore">Singapore</MenuItem>
                  <MenuItem value="Thailand">Thái Lan</MenuItem>
                  <MenuItem value="Malaysia">Malaysia</MenuItem>
                  <MenuItem value="Other">Khác</MenuItem>
                </Field.Select>

                <Field.Text
                  name="notes"
                  label="Ghi chú giao hàng"
                  multiline
                  rows={3}
                  placeholder="Ghi chú đặc biệt cho việc giao hàng (thời gian, hướng dẫn tìm đường...)"
                  helperText="VD: Giao hàng sau 18h, gọi trước 15 phút"
                />
              </Stack>
            </Box>

            <DialogActions sx={{ px: 3, pb: 3 }}>
              <Button onClick={handleClose} color="inherit">
                Hủy
              </Button>
              <LoadingButton
                type="submit"
                variant="contained"
                loading={isMutating}
                startIcon={<Iconify icon="solar:check-circle-bold" />}
              >
                Cập nhật địa chỉ
              </LoadingButton>
            </DialogActions>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
}

OrderEditAddressDialog.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  order: PropTypes.object,
  shippingAddress: PropTypes.object,
  billingAddress: PropTypes.object,
  onSuccess: PropTypes.func,
};
