'use client';

import PropTypes from 'prop-types';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Tooltip from '@mui/material/Tooltip';
import IconButton from '@mui/material/IconButton';

import { Iconify } from 'src/components/iconify';

import { formatFullAddress } from '../utils/address-formatter';

// ----------------------------------------------------------------------

export function AddressQuickActions({ 
  address, 
  onEdit, 
  onCopy, 
  onViewOnMap,
  showLabels = false,
  size = 'medium'
}) {
  if (!address) {
    return null;
  }

  const fullAddress = formatFullAddress(address);

  const handleCopyAddress = () => {
    if (navigator.clipboard) {
      navigator.clipboard.writeText(fullAddress);
      if (onCopy) {
        onCopy(fullAddress);
      }
    }
  };

  const handleViewOnMap = () => {
    const query = encodeURIComponent(fullAddress);
    const mapUrl = `https://www.google.com/maps/search/?api=1&query=${query}`;
    window.open(mapUrl, '_blank');
    
    if (onViewOnMap) {
      onViewOnMap(mapUrl);
    }
  };

  const buttonSize = size === 'small' ? 'small' : 'medium';
  const iconSize = size === 'small' ? 16 : 20;

  if (showLabels) {
    return (
      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
        {onEdit && (
          <Button
            size={buttonSize}
            startIcon={<Iconify icon="solar:pen-bold" width={iconSize} />}
            onClick={onEdit}
            variant="outlined"
            color="primary"
          >
            Chỉnh sửa
          </Button>
        )}
        
        <Button
          size={buttonSize}
          startIcon={<Iconify icon="solar:copy-bold" width={iconSize} />}
          onClick={handleCopyAddress}
          variant="outlined"
          color="inherit"
        >
          Sao chép
        </Button>
        
        <Button
          size={buttonSize}
          startIcon={<Iconify icon="solar:map-point-bold" width={iconSize} />}
          onClick={handleViewOnMap}
          variant="outlined"
          color="inherit"
        >
          Xem bản đồ
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ display: 'flex', gap: 0.5 }}>
      {onEdit && (
        <Tooltip title="Chỉnh sửa địa chỉ">
          <IconButton
            size={buttonSize}
            onClick={onEdit}
            color="primary"
          >
            <Iconify icon="solar:pen-bold" width={iconSize} />
          </IconButton>
        </Tooltip>
      )}
      
      <Tooltip title="Sao chép địa chỉ">
        <IconButton
          size={buttonSize}
          onClick={handleCopyAddress}
          color="inherit"
        >
          <Iconify icon="solar:copy-bold" width={iconSize} />
        </IconButton>
      </Tooltip>
      
      <Tooltip title="Xem trên bản đồ">
        <IconButton
          size={buttonSize}
          onClick={handleViewOnMap}
          color="inherit"
        >
          <Iconify icon="solar:map-point-bold" width={iconSize} />
        </IconButton>
      </Tooltip>
    </Box>
  );
}

AddressQuickActions.propTypes = {
  address: PropTypes.object,
  onEdit: PropTypes.func,
  onCopy: PropTypes.func,
  onViewOnMap: PropTypes.func,
  showLabels: PropTypes.bool,
  size: PropTypes.oneOf(['small', 'medium']),
};

// ----------------------------------------------------------------------

export function AddressActionMenu({ address, onEdit, onCopy, onViewOnMap }) {
  if (!address) {
    return null;
  }

  const fullAddress = formatFullAddress(address);

  const handleCopyAddress = () => {
    if (navigator.clipboard) {
      navigator.clipboard.writeText(fullAddress);
      if (onCopy) {
        onCopy(fullAddress);
      }
    }
  };

  const handleViewOnMap = () => {
    const query = encodeURIComponent(fullAddress);
    const mapUrl = `https://www.google.com/maps/search/?api=1&query=${query}`;
    window.open(mapUrl, '_blank');
    
    if (onViewOnMap) {
      onViewOnMap(mapUrl);
    }
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, minWidth: 150 }}>
      {onEdit && (
        <Button
          fullWidth
          startIcon={<Iconify icon="solar:pen-bold" />}
          onClick={onEdit}
          variant="text"
          color="primary"
          sx={{ justifyContent: 'flex-start' }}
        >
          Chỉnh sửa địa chỉ
        </Button>
      )}
      
      <Button
        fullWidth
        startIcon={<Iconify icon="solar:copy-bold" />}
        onClick={handleCopyAddress}
        variant="text"
        color="inherit"
        sx={{ justifyContent: 'flex-start' }}
      >
        Sao chép địa chỉ
      </Button>
      
      <Button
        fullWidth
        startIcon={<Iconify icon="solar:map-point-bold" />}
        onClick={handleViewOnMap}
        variant="text"
        color="inherit"
        sx={{ justifyContent: 'flex-start' }}
      >
        Xem trên bản đồ
      </Button>
    </Box>
  );
}

AddressActionMenu.propTypes = {
  address: PropTypes.object,
  onEdit: PropTypes.func,
  onCopy: PropTypes.func,
  onViewOnMap: PropTypes.func,
};
