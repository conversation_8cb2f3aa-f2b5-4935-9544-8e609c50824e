'use client';

import PropTypes from 'prop-types';

import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';
import Alert from '@mui/material/Alert';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

import { Iconify } from 'src/components/iconify';

import { validateAddressCompleteness } from '../utils/address-formatter';

// ----------------------------------------------------------------------

export function AddressValidationStatus({ address, showDetails = false }) {
  if (!address) {
    return (
      <Alert severity="error" icon={<Iconify icon="solar:close-circle-bold" />}>
        Chưa có thông tin địa chỉ giao hàng
      </Alert>
    );
  }

  const validation = validateAddressCompleteness(address);

  if (validation.isComplete) {
    return (
      <Stack direction="row" alignItems="center" spacing={1}>
        <Chip
          icon={<Iconify icon="solar:check-circle-bold" />}
          label="Địa chỉ đầy đủ"
          color="success"
          variant="filled"
          size="small"
        />
        {showDetails && validation.warnings.length > 0 && (
          <Typography variant="caption" color="warning.main">
            {validation.warnings.length} lưu ý
          </Typography>
        )}
      </Stack>
    );
  }

  return (
    <Stack spacing={1}>
      <Chip
        icon={<Iconify icon="solar:info-circle-bold" />}
        label="Địa chỉ chưa đầy đủ"
        color="warning"
        variant="filled"
        size="small"
      />
      
      {showDetails && (
        <Box>
          {validation.missingFields.length > 0 && (
            <Alert severity="error" sx={{ mb: 1 }}>
              <Typography variant="body2" fontWeight="medium">
                Thiếu thông tin bắt buộc:
              </Typography>
              <Typography variant="body2">
                {validation.missingFields.map(field => {
                  const fieldNames = {
                    fullName: 'Tên người nhận',
                    phone: 'Số điện thoại',
                    address: 'Địa chỉ cụ thể',
                  };
                  return fieldNames[field] || field;
                }).join(', ')}
              </Typography>
            </Alert>
          )}
          
          {validation.warnings.length > 0 && (
            <Alert severity="warning">
              <Typography variant="body2" fontWeight="medium">
                Lưu ý:
              </Typography>
              <Typography variant="body2">
                {validation.warnings.join(', ')}
              </Typography>
            </Alert>
          )}
        </Box>
      )}
    </Stack>
  );
}

AddressValidationStatus.propTypes = {
  address: PropTypes.object,
  showDetails: PropTypes.bool,
};

// ----------------------------------------------------------------------

export function AddressCompletenessIndicator({ address }) {
  if (!address) {
    return (
      <Box
        sx={{
          width: 8,
          height: 8,
          borderRadius: '50%',
          bgcolor: 'error.main',
        }}
      />
    );
  }

  const validation = validateAddressCompleteness(address);
  
  return (
    <Box
      sx={{
        width: 8,
        height: 8,
        borderRadius: '50%',
        bgcolor: validation.isComplete ? 'success.main' : 'warning.main',
      }}
    />
  );
}

AddressCompletenessIndicator.propTypes = {
  address: PropTypes.object,
};
